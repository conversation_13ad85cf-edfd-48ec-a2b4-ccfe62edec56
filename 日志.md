SPI Flash OK
Flash data: 0 unread
Manual N command cleared and saved to Flash: threshold=0, enabled=0
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
=== Manual Work Time Setup ===
Manual work time saved: 00:00 - 00:00 (Full day active)
=== End Manual Work Time Setup ===
RTOS OK
Main Task OK
Loading settings from FLASH on boot...
Loaded sleep setting: 30 seconds
Boot: wakeup_counter set to 29
Loaded work time setting: 0:00 - 0:00
Loaded pack count setting: disabled (N0)
Loaded speed threshold setting: 10 knots, enabled=1
Loaded fast send sleep time setting: 300 seconds
Historical Data: 0
===== Cycle #1 (Wakeup #0, Time: 0 min) =====
Current RTC time: 00:00:04 01/01/25 (DEFAULT - NOT SYNCED)
Battery: 3.911 V
Both GPS and RTC time invalid, work time check disabled
RTC not synced, need GPS sync for accurate time
First boot - attempting to get CCID...
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

CCID obtained: 89430103223249458271
TX: AT+CSQ
RX:
+CSQ: 28,0

OK

Signal quality: 28
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 300 seconds (first boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.2, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=6
GPS data after collection: result=0, valid=1, time=07:17:57
GPS time updated successfully
GPS data before sync: valid=1, time=07:17:57 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 07:17:57 18/08/25
Work time disabled (start==end: 0:00), normal operation enabled
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 27,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
First reasonable date accepted: 18/08/25
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+S30+F08E09

ZL command received: ZL+S30+F08E09
Work time configured: 08:00 - 09:00, current: 07:17:57 (RTC time)
Calculated smart sleep time: 2523 seconds (0.7 hours) until work time
Real-time data sent successfully
TX Data output: HY114S+11957.71680+3016.56592+071757.180825+43.9+1+6+0.0+3.91+33.3+2.0+3.0+-5.2+-0.3+0.0+0.00+27+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=07:17:57 18/08/2025
GPS data cleared
Time before sleep: 07:18:22
Work time configured: 08:00 - 09:00, current: 07:17:57 (RTC time)
Calculated smart sleep time: 2523 seconds (0.7 hours) until work time
Using pre-set sleep time: 2523 seconds
Sleep for 2523 seconds
Woke up from sleep mode
Time after sleep: 08:07:33
Elapsed time: 2951 seconds
=======================END==========================


Cycle #1 completed

===== Cycle #2 (Wakeup #1, Time: 0 min) =====
Current RTC time: 08:07:33 18/08/25
Battery: 3.936 V
Work time configured: 08:00 - 09:00, current: 08:07:33 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=6
GPS data after collection: result=0, valid=1, time=07:57:40
GPS time updated successfully
GPS data before sync: valid=1, time=07:57:40 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 07:57:40 18/08/25
Work time configured: 08:00 - 09:00, current: 07:57:40 (RTC time)
Calculated smart sleep time: 140 seconds (0.0 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 07:57:40
Work time configured: 08:00 - 09:00, current: 07:57:40 (RTC time)
Calculated smart sleep time: 140 seconds (0.0 hours) until work time
Using pre-set sleep time: 140 seconds
Sleep for 140 seconds
Woke up from sleep mode
Time after sleep: 08:00:23
Elapsed time: 163 seconds
=======================END==========================


===== Cycle #3 (Wakeup #2, Time: 1 min) =====
Current RTC time: 08:00:23 18/08/25
Battery: 3.934 V
Work time configured: 08:00 - 09:00, current: 08:00:23 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=5
GPS data after collection: result=0, valid=1, time=08:00:07
GPS time updated successfully
GPS data before sync: valid=1, time=08:00:07 47/00/0000
RTC synchronized with GPS time
RTC synced to GPS time: 08:00:07 18/08/25
Work time configured: 08:00 - 09:00, current: 08:00:07 (RTC time)
Current time is within work hours
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 28,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
RTC date validated, updating existing backup
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+S30+F08E09

ZL command received: ZL+S30+F08E09
Work time configured: 08:00 - 09:00, current: 08:00:07 (RTC time)
Current time is within work hours
Real-time data sent successfully
TX Data output: HY114S+11957.71680+3016.56177+080007.180825+23.0+1+5+0.0+3.93+30.3+4.7+3.0+-5.3+-0.2+0.0+0.00+28+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=08:00:07 47/00/0000
GPS data cleared
Time before sleep: 08:00:37
Work time configured: 08:00 - 09:00, current: 08:00:07 (RTC time)
Current time is within work hours
Using pre-set sleep time: 30 seconds
Sleep for 30 seconds
Woke up from sleep mode
Time after sleep: 08:01:11
Elapsed time: 34 seconds
=======================END==========================
