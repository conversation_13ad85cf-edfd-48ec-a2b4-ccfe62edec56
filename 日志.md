SPI Flash OK
Flash data: 0 unread
Manual N command cleared and saved to Flash: threshold=0, enabled=0
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
=== Manual Work Time Setup ===
Manual work time saved: 00:00 - 00:00 (Full day active)
=== End Manual Work Time Setup ===
RTOS OK
Main Task OK
Loading settings from FLASH on boot...
Loaded sleep setting: 30 seconds
Boot: wakeup_counter set to 29
Loaded work time setting: 0:00 - 0:00
Loaded pack count setting: disabled (N0)
Loaded speed threshold setting: 10 knots, enabled=1
Loaded fast send sleep time setting: 300 seconds
Historical Data: 0
===== Cycle #1 (Wakeup #0, Time: 0 min) =====
Current RTC time: 00:00:03 01/01/25 (DEFAULT - NOT SYNCED)
Battery: 3.868 V
Both GPS and RTC time invalid, work time check disabled
RTC not synced, need GPS sync for accurate time
First boot - attempting to get CCID...
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE?

O

TX: AT+CCID
RX:
89430103223249458271

OK

CCID obtained: 89430103223249458271
TX: AT+CSQ
RX:
+CSQ: 31,0

OK

Signal quality: 31
GPS: Power ON
GPS wait: 300 seconds (first boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-1.4, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=5
Work time disabled (start==end: 0:00), normal operation enabled
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 29,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
First reasonable date accepted: 15/08/25
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+M30+F00E02+N0

ZL command received: ZL+M30+F00E02+N0
Sleep minutes command: 30 minutes
Work time configured: 00:00 - 02:00, current: 09:18:05 (GPS time)
Calculated smart sleep time: 52915 seconds (14.7 hours) until work time
Pack count command: disabled (N0)
Pack count setting updated: disabled (N0)
Real-time data sent successfully
TX Data output: HY114S+11957.71484+3016.56641+091805.150825+75.2+1+5+0.0*****+27.3********+-1.4+-0.3+0.0+0.00+29+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data cleared
Time before sleep: 09:18:19
Both GPS and RTC time invalid, work time check disabled
Using pre-set sleep time: 52915 seconds
Sleep for 52915 seconds
Woke up from sleep mode
Time after sleep: 02:30:09
Elapsed time: 61910 seconds
=======================END==========================


Cycle #1 completed

===== Cycle #2 (Wakeup #1, Time: 30 min) =====
Current RTC time: 02:30:09 16/08/25
Battery: 3.935 V
Work time configured: 00:00 - 02:00, current: 02:30:09 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-1.9, Pitch=-0.8
GPS signal waiting......
GPS estimated fix stored: quality=6, satellites=6 (waiting for quality 1)
GPS precise fix: quality=1, satellites=6
Work time configured: 00:00 - 02:00, current: 02:30:09 (RTC time)
Current time is within work hours
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 27,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+M30+F00E02+N0

ZL command received: ZL+M30+F00E02+N0
Sleep minutes command: 30 minutes
Work time configured: 00:00 - 02:00, current: 02:30:09 (RTC time)
Current time is within work hours
Pack count command: disabled (N0)
Pack count setting updated: disabled (N0)
Real-time data sent successfully
TX Data output: HY114S+11957.72949+3016.55493+225137.150825+54.0+1+6+0.0*****+29.4********+-1.9+-0.8+0.0+0.00+27+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data cleared
Time before sleep: 22:51:53
Work time configured: 00:00 - 02:00, current: 02:30:09 (RTC time)
Current time is within work hours
Using pre-set sleep time: 1800 seconds
Sleep for 1800 seconds
Woke up from sleep mode
Time after sleep: 23:26:58
Elapsed time: 2105 seconds
=======================END==========================


Cycle #2 completed

===== Cycle #3 (Wakeup #2, Time: 60 min) =====
Current RTC time: 23:26:58 15/08/25
Battery: 3.937 V
Work time configured: 00:00 - 02:00, current: 23:26:58 (RTC time)
Calculated smart sleep time: 1982 seconds (0.6 hours) until work time
Smart sleep calculated but need GPS sync for time update
GPS: Power ON
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-2.2, Pitch=-1.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=9
Work time configured: 00:00 - 02:00, current: 23:26:58 (RTC time)
Calculated smart sleep time: 1982 seconds (0.6 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
