SPI Flash OK
Flash data: 0 unread
Manual N command cleared and saved to Flash: threshold=0, enabled=0
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
=== Manual Work Time Setup ===
Manual work time saved: 00:00 - 00:00 (Full day active)
=== End Manual Work Time Setup ===
RTOS OK
Main Task OK
Loading settings from FLASH on boot...
Loaded sleep setting: 30 seconds
Boot: wakeup_counter set to 29
Loaded work time setting: 0:00 - 0:00
Loaded pack count setting: disabled (N0)
Loaded speed threshold setting: 10 knots, enabled=1
Loaded fast send sleep time setting: 300 seconds
Historical Data: 0
===== Cycle #1 (Wakeup #0, Time: 0 min) =====
Current RTC time: 00:00:03 01/01/25 (DEFAULT - NOT SYNCED)
Battery: 3.901 V
Both GPS and RTC time invalid, work time check disabled
RTC not synced, need GPS sync for accurate time
First boot - attempting to get CCID...
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK?

TX: AT+CCID
RX:
89430103223249458271

OK

CCID obtained: 89430103223249458271
TX: AT+CSQ
RX:
+CSQ: 29,0

OK

Signal quality: 29
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS wait: 300 seconds (first boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:09:18
GPS data before sync: valid=1, time=06:09:18 18/08/2025
RTC synced to GPS time: 06:09:18 18/08/25
Work time disabled (start==end: 0:00), normal operation enabled
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 29,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
First reasonable date accepted: 18/08/25
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+S30+F07E08

ZL command received: ZL+S30+F07E08
Work time configured: 07:00 - 08:00, current: 06:09:18 (RTC time)
Calculated smart sleep time: 3042 seconds (0.8 hours) until work time
Real-time data sent successfully
TX Data output: HY114S+11957.70605+3016.56592+060918.180825+47.3+1+7+0.0+3.90+32.4+9.2+3.0+-5.3+-0.3+0.0+0.00+29+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=06:09:18 18/08/2025
GPS data cleared
Time before sleep: 06:09:31
Work time configured: 07:00 - 08:00, current: 06:09:18 (RTC time)
Calculated smart sleep time: 3042 seconds (0.8 hours) until work time
Using pre-set sleep time: 3042 seconds
Sleep for 3042 seconds
Woke up from sleep mode
Time after sleep: 07:08:49
Elapsed time: 3558 seconds
=======================END==========================


Cycle #1 completed

===== Cycle #2 (Wakeup #1, Time: 0 min) =====
Current RTC time: 07:08:49 18/08/25
Battery: 3.935 V
Work time configured: 07:00 - 08:00, current: 07:08:49 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
Woke up from sleep mode
Time after sleep: 07:00:31
Elapsed time: 218 seconds
=======================END==========================


===== Cycle #3 (Wakeup #2, Time: 1 min) =====
Current RTC time: 07:00:31 18/08/25
Battery: 3.934 V
Work time configured: 07:00 - 08:00, current: 07:00:31 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=1, time=06:56:53
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
Woke up from sleep mode
Time after sleep: 07:00:31
Elapsed time: 218 seconds
=======================END==========================


===== Cycle #4 (Wakeup #3, Time: 1 min) =====
Current RTC time: 07:00:31 18/08/25
Battery: 3.936 V
Work time configured: 07:00 - 08:00, current: 07:00:31 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=1, time=06:56:53
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.1
GPS signal waiting......
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
Woke up from sleep mode
Time after sleep: 07:00:31
Elapsed time: 218 seconds
=======================END==========================


===== Cycle #5 (Wakeup #4, Time: 2 min) =====
Current RTC time: 07:00:31 18/08/25
Battery: 3.934 V
Work time configured: 07:00 - 08:00, current: 07:00:31 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=1, time=06:56:53
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.1
GPS signal waiting......
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
Woke up from sleep mode
Time after sleep: 07:00:31
Elapsed time: 218 seconds
=======================END==========================


===== Cycle #6 (Wakeup #5, Time: 2 min) =====
Current RTC time: 07:00:31 18/08/25
Battery: 3.935 V
Work time configured: 07:00 - 08:00, current: 07:00:31 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=1, time=06:56:53
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.1
GPS signal waiting......
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
Woke up from sleep mode
Time after sleep: 07:00:31
Elapsed time: 218 seconds
=======================END==========================


===== Cycle #7 (Wakeup #6, Time: 3 min) =====
Current RTC time: 07:00:31 18/08/25
Battery: 3.935 V
Work time configured: 07:00 - 08:00, current: 07:00:31 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=1, time=06:56:53
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:56:53
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.1
GPS signal waiting......
GPS data before sync: valid=1, time=06:56:53 18/08/2025
RTC synced to GPS time: 06:56:53 18/08/25
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 06:56:53
Work time configured: 07:00 - 08:00, current: 06:56:53 (RTC time)
Calculated smart sleep time: 187 seconds (0.1 hours) until work time
Using pre-set sleep time: 187 seconds
Sleep for 187 seconds
