
===== Cycle #7 (Wakeup #6, Time: 3 min) =====
Current RTC time: 09:00:38 18/08/25
Battery: 3.936 V
Work time configured: 09:00 - 10:00, current: 09:00:38 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.1
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=09:00:03
GPS time updated successfully
GPS data before sync: valid=1, time=09:00:03 42/00/0000
RTC synchronized with GPS time
RTC synced to GPS time: 09:00:03 18/08/25
Work time configured: 09:00 - 10:00, current: 09:00:03 (RTC time)
Current time is within work hours
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 28,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
RTC date validated, updating existing backup
GSM send: AT+CIPSEND=119
TX: AT+CIPSEND=119
RX:

DATA ACCEPT:119


ZL+S10+F23E02+N20

ZL command received: ZL+S10+F23E02+N20
Work time configured: 23:00 - 02:00, current: 09:00:03 (RTC time)
Calculated smart sleep time: 50397 seconds (14.0 hours) until work time
Pack count command: 20 records
Pack count setting updated: threshold=20, enabled
Real-time data sent successfully
TX Data output: HY114S+11957.72363+3016.56348+090003.180825+19.0+1+7+0.0*****+30.3+3.2****+-5.3+-0.1+0.0+0.00+28+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=09:00:03 42/00/0000
GPS data cleared
Time before sleep: 09:00:24
Work time configured: 23:00 - 02:00, current: 09:00:03 (RTC time)
Calculated smart sleep time: 50397 seconds (14.0 hours) until work time
Using pre-set sleep time: 50397 seconds
Sleep for 50397 seconds
Woke up from sleep mode
Time after sleep: 01:23:08
Elapsed time: 58964 seconds
=======================END==========================


Cycle #7 completed

===== Cycle #8 (Wakeup #7, Time: 1 min) =====
Current RTC time: 01:23:08 19/08/25
Battery: 3.936 V
Work time configured: 23:00 - 02:00, current: 01:23:08 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS estimated fix stored: quality=6, satellites=5 (waiting for quality 1)
GPS precise fix: quality=1, satellites=5
GPS data after collection: result=0, valid=1, time=21:53:58
GPS time updated successfully
GPS data before sync: valid=1, time=21:53:58 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 21:53:58 18/08/25
Work time configured: 23:00 - 02:00, current: 21:53:58 (RTC time)
Calculated smart sleep time: 3962 seconds (1.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 21:53:58
Work time configured: 23:00 - 02:00, current: 21:53:58 (RTC time)
Calculated smart sleep time: 3962 seconds (1.1 hours) until work time
Using pre-set sleep time: 3962 seconds
Sleep for 3962 seconds
Woke up from sleep mode
Time after sleep: 23:11:13
Elapsed time: 4635 seconds
=======================END==========================


===== Cycle #9 (Wakeup #8, Time: 1 min) =====
Current RTC time: 23:11:13 18/08/25
Battery: 3.936 V
Work time configured: 23:00 - 02:00, current: 23:11:13 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS estimated fix stored: quality=6, satellites=5 (waiting for quality 1)
GPS precise fix: quality=1, satellites=8
GPS data after collection: result=0, valid=1, time=22:55:20
GPS time updated successfully
GPS data before sync: valid=1, time=22:55:20 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 22:55:20 18/08/25
Work time configured: 23:00 - 02:00, current: 22:55:20 (RTC time)
Calculated smart sleep time: 280 seconds (0.1 hours) until work time
Post-GPS check: Not in work hours, skipping GSM and data processing
Final check: GPS task determined not in work hours, entering sleep mode
Time before sleep: 22:55:20
Work time configured: 23:00 - 02:00, current: 22:55:20 (RTC time)
Calculated smart sleep time: 280 seconds (0.1 hours) until work time
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:00:47
Elapsed time: 327 seconds
=======================END==========================


===== Cycle #10 (Wakeup #9, Time: 1 min) =====
Current RTC time: 23:00:47 18/08/25
Battery: 3.937 V
Work time configured: 23:00 - 02:00, current: 23:00:47 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS estimated fix stored: quality=6, satellites=5 (waiting for quality 1)
GPS precise fix: quality=1, satellites=6
GPS data after collection: result=0, valid=1, time=23:00:05
GPS time updated successfully
GPS data before sync: valid=1, time=23:00:05 18/08/0225
RTC synchronized with GPS time
RTC synced to GPS time: 23:00:05 18/08/25
Work time configured: 23:00 - 02:00, current: 23:00:05 (RTC time)
Current time is within work hours (cross-day)
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM task skipped due to N command
RTC date validated, updating existing backup
N command active - storing data directly to SPI Flash
SPI Flash Write: RecordIndex=0, WriteAddr=0x00000000, TotalRecords=0, ReadRecords=0
Data cached to SPI Flash (send failed, Unread: 1)
TX Data output: HY113B+11957.70605+3016.55664+230005.180825****+1+6+0.0*****+30.3********+-5.3+-0.2+0.1+0.00+28+89430103223249458271+E
GPS data before clear: valid=1, time=23:00:05 18/08/0225
GPS data cleared
Time before sleep: 23:00:06
Work time configured: 23:00 - 02:00, current: 23:00:05 (RTC time)
Current time is within work hours (cross-day)
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:05:33
Elapsed time: 327 seconds
=======================END==========================


Cycle #10 completed

===== Cycle #11 (Wakeup #10, Time: 1 min) =====
Current RTC time: 23:05:33 18/08/25
Battery: 3.937 V
Work time configured: 23:00 - 02:00, current: 23:05:33 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=23:04:59
GPS time updated successfully
GPS data before sync: valid=1, time=23:04:59 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 23:04:59 18/08/25
Work time configured: 23:00 - 02:00, current: 23:04:59 (RTC time)
Current time is within work hours (cross-day)
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM task skipped due to N command
N command active - storing data directly to SPI Flash
SPI Flash Write: RecordIndex=1, WriteAddr=0x00000082, TotalRecords=1, ReadRecords=0
Data cached to SPI Flash (send failed, Unread: 2)
TX Data output: HY113B+11957.72949+3016.56714+230459.180825****+1+7+0.0*****+30.3********+-5.3+-0.2+0.1+0.00+28+89430103223249458271+E
GPS data before clear: valid=1, time=23:04:59 18/08/2025
GPS data cleared
Time before sleep: 23:05:00
Work time configured: 23:00 - 02:00, current: 23:04:59 (RTC time)
Current time is within work hours (cross-day)
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:10:27
Elapsed time: 327 seconds
=======================END==========================


Cycle #11 completed

===== Cycle #12 (Wakeup #11, Time: 1 min) =====
Current RTC time: 23:10:27 18/08/25
Battery: 3.936 V
Work time configured: 23:00 - 02:00, current: 23:10:27 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.2, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=6
GPS data after collection: result=0, valid=1, time=23:09:38
GPS time updated successfully
GPS data before sync: valid=1, time=23:09:38 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 23:09:38 18/08/25
Work time configured: 23:00 - 02:00, current: 23:09:38 (RTC time)
Current time is within work hours (cross-day)
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM task skipped due to N command
N command active - storing data directly to SPI Flash
SPI Flash Write: RecordIndex=2, WriteAddr=0x00000104, TotalRecords=2, ReadRecords=0
Data cached to SPI Flash (send failed, Unread: 3)
TX Data output: HY114B+11957.71973+3016.56665+230938.180825+74.2+1+6+0.0*****+30.3********+-5.2+-0.2+0.1+0.00+28+89430103223249458271+E
GPS data before clear: valid=1, time=23:09:38 18/08/2025
GPS data cleared
Time before sleep: 23:09:39
Work time configured: 23:00 - 02:00, current: 23:09:38 (RTC time)
Current time is within work hours (cross-day)
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:15:06
Elapsed time: 327 seconds
=======================END==========================


Cycle #12 completed

===== Cycle #13 (Wakeup #12, Time: 2 min) =====
Current RTC time: 23:15:06 18/08/25
Battery: 3.937 V
Work time configured: 23:00 - 02:00, current: 23:15:06 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=23:14:19
GPS time updated successfully
GPS data before sync: valid=1, time=23:14:19 44/00/0000
RTC synchronized with GPS time
RTC synced to GPS time: 23:14:19 18/08/25
Work time configured: 23:00 - 02:00, current: 23:14:19 (RTC time)
Current time is within work hours (cross-day)
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM task skipped due to N command
RTC date validated, updating existing backup
N command active - storing data directly to SPI Flash
SPI Flash Write: RecordIndex=3, WriteAddr=0x00000186, TotalRecords=3, ReadRecords=0
Data cached to SPI Flash (send failed, Unread: 4)
TX Data output: HY114B+11957.71484+3016.57227+231419.180825+32.1+1+7+0.0*****+30.3********+-5.3+-0.2+0.1+0.00+28+89430103223249458271+E
GPS data before clear: valid=1, time=23:14:19 44/00/0000
GPS data cleared
Time before sleep: 23:14:20
Work time configured: 23:00 - 02:00, current: 23:14:19 (RTC time)
Current time is within work hours (cross-day)
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:19:47
Elapsed time: 327 seconds
=======================END==========================


Cycle #13 completed

===== Cycle #14 (Wakeup #13, Time: 2 min) =====
Current RTC time: 23:19:47 18/08/25
Battery: 3.937 V
Work time configured: 23:00 - 02:00, current: 23:19:47 (RTC time)
Current time is within work hours (cross-day)
Within work hours, need GPS sync
Skip GSM this cycle - N command active
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS data fully reset before collection
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.3, Pitch=-0.2
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=23:18:57
GPS time updated successfully
GPS data before sync: valid=1, time=23:18:57 18/08/2025
RTC synchronized with GPS time
RTC synced to GPS time: 23:18:57 18/08/25
Work time configured: 23:00 - 02:00, current: 23:18:57 (RTC time)
Current time is within work hours (cross-day)
Post-GPS check: In work hours, continuing normal operation
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM task skipped due to N command
N command active - storing data directly to SPI Flash
SPI Flash Write: RecordIndex=4, WriteAddr=0x00000208, TotalRecords=4, ReadRecords=0
Data cached to SPI Flash (send failed, Unread: 5)
TX Data output: HY114B+11957.71875+3016.56348+231857.180825+12.1+1+7+0.0*****+30.7********+-5.3+-0.2+0.1+0.00+28+89430103223249458271+E
GPS data before clear: valid=1, time=23:18:57 18/08/2025
GPS data cleared
Time before sleep: 23:18:58
Work time configured: 23:00 - 02:00, current: 23:18:57 (RTC time)
Current time is within work hours (cross-day)
Using pre-set sleep time: 280 seconds
Sleep for 280 seconds
Woke up from sleep mode
Time after sleep: 23:24:25
Elapsed time: 327 seconds
=======================END==========================
