SPI Flash OK
Flash data: 0 unread
Manual N command cleared and saved to Flash: threshold=0, enabled=0
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
=== Manual Work Time Setup ===
Manual work time saved: 00:00 - 00:00 (Full day active)
=== End Manual Work Time Setup ===
RTOS OK
Main Task OK
Loading settings from FLASH on boot...
Loaded sleep setting: 30 seconds
Boot: wakeup_counter set to 29
Loaded work time setting: 0:00 - 0:00
Loaded pack count setting: disabled (N0)
Loaded speed threshold setting: 10 knots, enabled=1
Loaded fast send sleep time setting: 300 seconds
Historical Data: 0
===== Cycle #1 (Wakeup #0, Time: 0 min) =====
Current RTC time: 00:00:02 01/01/25 (DEFAULT - NOT SYNCED)
Battery: 3.886 V
Both GPS and RTC time invalid, work time check disabled
RTC not synced, need GPS sync for accurate time
First boot - attempting to get CCID...
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89殏妭殥挌挗盛捄妀

OK

Failed to get CCID, using default
TX: AT+CSQ
RX:
+CSQ: 28,0

OK

Signal quality: 28
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS wait: 300 seconds (first boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.4, Pitch=-0.4
GPS signal waiting......
GPS estimated fix stored: quality=6, satellites=4 (waiting for quality 1)
GPS precise fix: quality=1, satellites=4
GPS data after collection: result=0, valid=1, time=05:23:10
GPS data before sync: valid=1, time=05:23:10 18/08/2025
RTC synced to GPS time: 05:23:10 18/08/25
DEBUG: Temporarily bypassing work time check for GPS time debugging
Post-GPS check: Forced to continue operation for debugging
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 28,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
First reasonable date accepted: 18/08/25
GSM send: AT+CIPSEND=120
TX: AT+CIPSEND=120
RX:

DATA ACCEPT:120


ZL+S30+F06E07

ZL command received: ZL+S30+F06E07
Work time configured: 06:00 - 07:00, current: 05:23:10 (RTC time)
Calculated smart sleep time: 2210 seconds (0.6 hours) until work time
Real-time data sent successfully
TX Data output: HY115S+11957.71484+3016.57764+052310.180825+103.0+1+4+0.0+3.89+30.7+3.5****+-5.4+-0.4+0.0+0.00+28+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=05:23:10 18/08/2025
GPS data cleared
Time before sleep: 05:23:23
Work time configured: 06:00 - 07:00, current: 05:23:10 (RTC time)
Calculated smart sleep time: 2210 seconds (0.6 hours) until work time
Using pre-set sleep time: 2210 seconds
Sleep for 2210 seconds
Woke up from sleep mode
Time after sleep: 06:06:29
Elapsed time: 2585 seconds
=======================END==========================


Cycle #1 completed

===== Cycle #2 (Wakeup #1, Time: 0 min) =====
Current RTC time: 06:06:29 18/08/25
Battery: 3.925 V
Work time configured: 06:00 - 07:00, current: 06:06:29 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.4, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=8
GPS data after collection: result=0, valid=1, time=05:58:03
GPS data before sync: valid=1, time=05:58:03 18/08/2025
RTC synced to GPS time: 05:58:03 18/08/25
DEBUG: Temporarily bypassing work time check for GPS time debugging
Post-GPS check: Forced to continue operation for debugging
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 28,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
GSM send: AT+CIPSEND=120
TX: AT+CIPSEND=120
RX:

DATA ACCEPT:120


ZL+S30+F06E07

ZL command received: ZL+S30+F06E07
Work time configured: 06:00 - 07:00, current: 05:58:03 (RTC time)
Calculated smart sleep time: 117 seconds (0.0 hours) until work time
Real-time data sent successfully
TX Data output: HY115S+11957.73828+3016.56177+055803.180825+-19.8+1+8+0.0*****+29.4********+-5.4+-0.3+0.0+0.00+28+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=05:58:03 18/08/2025
GPS data cleared
Time before sleep: 05:58:16
Work time configured: 06:00 - 07:00, current: 05:58:03 (RTC time)
Calculated smart sleep time: 117 seconds (0.0 hours) until work time
Using pre-set sleep time: 117 seconds
Sleep for 117 seconds
Woke up from sleep mode
Time after sleep: 06:00:33
Elapsed time: 136 seconds
=======================END==========================


Cycle #2 completed

===== Cycle #3 (Wakeup #2, Time: 1 min) =====
Current RTC time: 06:00:33 18/08/25
Battery: 3.927 V
Work time configured: 06:00 - 07:00, current: 06:00:33 (RTC time)
Current time is within work hours
Within work hours, need GPS sync
GPS: Power ON
GPS data before collection: valid=0, time=00:00:00
GPS wait: 120 seconds (normal boot)
GPS: Starting data reception...
LSM6DS3 int OK
Roll=-5.4, Pitch=-0.3
GPS signal waiting......
GPS precise fix: quality=1, satellites=7
GPS data after collection: result=0, valid=1, time=06:00:21
GPS data before sync: valid=1, time=06:00:21 04/00/0000
RTC synced to GPS time: 06:00:21 18/08/25
DEBUG: Temporarily bypassing work time check for GPS time debugging
Post-GPS check: Forced to continue operation for debugging
GPS task completed, power off
Final check: GPS task confirmed in work hours, proceeding with normal operation
GSM power: power on complete
TX: AT
RX:
AT

OK

TX: ATE0
RX: ATE0

OK

TX: AT+CCID
RX:
89430103223249458271

OK

TX: AT+CSQ
RX:
+CSQ: 28,0

OK

TX: AT+CIPSTART="TCP","************",48085
TX: AT+CIPQSEND=1
RTC date validated, updating existing backup
GSM send: AT+CIPSEND=120
TX: AT+CIPSEND=120
RX:

DATA ACCEPT:120


ZL+S30+F06E07

ZL command received: ZL+S30+F06E07
Work time configured: 06:00 - 07:00, current: 06:00:21 (RTC time)
Current time is within work hours
Real-time data sent successfully
TX Data output: HY115S+11957.71484+3016.56152+060021.180825+34.2+1+7+0.0*****+30.3+10.7****+-5.4+-0.3+0.0+0.00+28+89430103223249458271+E
AB Test Mode: Using virtual speed 9.0 knots (Real GPS: 0.0 knots)
Speed check: current=9.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
TX: AT+CIPCLOSE
GPS data before clear: valid=1, time=06:00:21 04/00/0000
GPS data cleared
Time before sleep: 06:00:34
Work time configured: 06:00 - 07:00, current: 06:00:21 (RTC time)
Current time is within work hours
Using pre-set sleep time: 30 seconds
Sleep for 30 seconds
