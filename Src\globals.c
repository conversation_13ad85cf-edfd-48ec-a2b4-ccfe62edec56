/**
  ******************************************************************************
  * @file    globals.c
  * @brief   全局变量定义
  ******************************************************************************
  */

#include "globals.h"

// 定义全局变量
char str[20];
uint8_t rx1_tmp[10];
uint8_t tx1_tmp[10];
uint8_t rx2_tmp[10];
uint8_t tx2_tmp[10];
uint8_t rx3_tmp[10];
uint8_t tx3_tmp[10];
uint8_t data;
uint32_t ADC_Value[60];  // 3通道 × 20采样 = 60个数据
int ad1, ad2, ad3, ad4, ad5, tp, td, button;
float pw;
float mcu_temp = 0.0f;  // MCU内部温度

// 单独读取俯仰角和横滚角到变量
float pitch ;  // 俯仰角
float roll ;    // 横滚角
float yaw ;      // 方位角

// 工作时间相关
unsigned int sleepSeconds = 10; // 默认休眠时间为10秒

// GSM模块相关全局变量
char gsm_ccid[32] = "TEST0001";        // GSM模块CCID号，默认值，设备启动时获取一次
int8_t gsm_signal_quality = -128;      // GSM信号质量 (-128表示无效，0-31表示信号强度)

// ZL自定义指令功能相关全局变量
uint32_t pack_count_threshold = 0;     // N指令：存储N条数据后再启动GSM模块（0=禁用）
uint8_t pack_count_enabled = 0;        // N指令是否启用标志
uint32_t speed_threshold_kmh = 0;      // A指令：速度阈值（节 knots，0=禁用）
uint32_t fast_send_sleep_time = 600;   // B指令：快发模式休眠时间（秒，默认600秒）
uint8_t speed_threshold_enabled = 0;   // A指令是否启用标志

// 长时间休眠跟踪机制
uint32_t target_sleep_seconds = 0;     // 目标休眠总时间（秒，0=无长时间休眠）
uint32_t elapsed_sleep_seconds = 0;    // 已休眠时间（秒）
uint8_t long_sleep_active = 0;         // 长时间休眠是否激活（0=否，1=是）
