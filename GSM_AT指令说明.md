## 本说明是Air780E  GSM模块的AT指令说明文档，请根据文档编制代码
## 下面的AT指令测试是通过串口助手连接模块实测数据
## 硬件连接使用LPUART1口
##################################################

[08:36:34.992]收←◆^boot.rom'v\0\0\0'!\n  //模块启动输出
[08:36:36.026]收←◆
RDY

[08:36:37.994]收←◆
^MODE: 17,17

[08:36:38.180]收←◆
+E_UTRAN Service

+CGEV: ME PDN ACT 1

+NITZ: 2025/07/18,00:36:37+0,0  //模块启动输出

[08:36:43.551]发→◇ATE0  //关闭回显
□
[08:36:43.557]收←◆ATE0  //回显1次

OK                       //关闭回显成功 后面都不会有回显

[08:36:44.584]发→◇AT+CGMM  //获取模组型号
□
[08:36:44.591]收←◆
+CGMM: "Air780EG"

OK

[08:36:45.287]发→◇AT+CCID  //获取CCID号
□
[08:36:45.295]收←◆
89860316249511500582

OK

[08:36:46.200]发→◇AT+CBC //获取电压
□
[08:36:46.207]收←◆
+CBC: 3489

OK

[08:36:47.488]发→◇AT+CSQ  //获取信号值
□
[08:36:47.496]收←◆
+CSQ: 17,0

OK

[08:36:50.136]发→◇AT+CIPSTART="TCP","**********",58085 //连接服务器
□
[08:36:50.147]收←◆
OK

[08:36:50.283]收←◆
CONNECT OK

[08:36:52.551]发→◇AT+CIPQSEND=1 //设置快发模式
□
[08:36:52.558]收←◆
OK

[08:37:00.488]发→◇AT+CIPSEND=122  //要先发送字节量 再发送数据 所以发送函数要自动计算数据长度后发送
□
[08:37:00.496]收←◆
>
[08:37:01.340]发→◇HY105S+12345.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E
□
[08:37:01.357]收←◆
DATA ACCEPT:122   //只是模块的回复

[08:37:01.478]收←◆ZL+H4+F0E0+N0  //这是服务器收到数据的回复

[08:37:04.336]发→◇AT+CIPCLOSE  //关闭tcp连接
□
[08:37:04.349]收←◆
CLOSE OK



