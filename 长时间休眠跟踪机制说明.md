# 长时间休眠跟踪机制说明

## 设计目标

解决STM32 RTC唤醒定时器16位限制（最大65535秒≈18.2小时）的问题，实现真正的长时间休眠指令执行。

## 核心思想

通过**分段休眠 + 进度跟踪**的方式，让设备能够执行超过18.2小时的休眠指令：
- 设备每18.2小时唤醒一次进行自检
- 检查是否达到目标休眠时间
- 如果未达到，继续休眠剩余时间
- 从服务器角度看，设备正常执行了长时间休眠指令

## 实现机制

### 1. 跟踪变量
```c
uint32_t target_sleep_seconds;   // 目标休眠总时间（秒）
uint32_t elapsed_sleep_seconds;  // 已休眠时间（秒）
uint8_t long_sleep_active;       // 长时间休眠是否激活
```

### 2. 工作流程

#### 步骤1: 指令接收
```
用户发送: ZL+D2 (2天 = 172,800秒)
↓
系统检测: 172,800 > 65535，启动长时间休眠跟踪
↓
设置: target_sleep_seconds = 172,800
      elapsed_sleep_seconds = 0
      long_sleep_active = 1
```

#### 步骤2: 第一次休眠
```
实际休眠: 65535秒 (18.2小时)
更新进度: elapsed_sleep_seconds = 65535
剩余时间: 172,800 - 65535 = 107,265秒
```

#### 步骤3: 第一次唤醒
```
设备唤醒 → 检查长时间休眠状态
↓
发现: elapsed_sleep_seconds (65535) < target_sleep_seconds (172,800)
↓
结果: 跳过所有功能，直接继续休眠
```

#### 步骤4: 第二次休眠
```
剩余时间: 107,265秒
实际休眠: 65535秒 (再次达到硬件限制)
更新进度: elapsed_sleep_seconds = 131,070
剩余时间: 172,800 - 131,070 = 41,730秒
```

#### 步骤5: 第二次唤醒
```
设备唤醒 → 检查长时间休眠状态
↓
发现: 仍未达到目标时间
↓
结果: 继续休眠
```

#### 步骤6: 第三次休眠
```
剩余时间: 41,730秒
实际休眠: 41,730秒 (小于硬件限制)
更新进度: elapsed_sleep_seconds = 172,800
```

#### 步骤7: 第三次唤醒
```
设备唤醒 → 检查长时间休眠状态
↓
发现: elapsed_sleep_seconds (172,800) >= target_sleep_seconds (172,800)
↓
结果: 长时间休眠完成，清除跟踪状态，正常运行
```

## 关键函数

### 1. 初始化跟踪
```c
void NetworkCommand_InitLongSleepTracking(uint32_t total_seconds);
```
- 当休眠时间 > 65535秒时自动启动
- 设置目标时间和初始进度

### 2. 检查休眠状态
```c
uint8_t NetworkCommand_CheckLongSleepStatus(void);
```
- 在设备唤醒后、电压检测前调用
- 返回1：需要继续休眠
- 返回0：休眠完成，可以正常运行

### 3. 更新进度
```c
void NetworkCommand_UpdateLongSleepProgress(uint32_t slept_seconds);
```
- 在每次进入休眠前调用
- 累加已休眠时间

### 4. 清除跟踪
```c
void NetworkCommand_ClearLongSleepTracking(void);
```
- 休眠完成后自动调用
- 重置所有跟踪变量

## 调试输出示例

### D2指令执行过程

#### 第一次休眠
```
Long sleep tracking initialized: target=172800 seconds (48.0 hours)
Sleep time limited to maximum: 65535 seconds (18.2 hours) - was 172800 seconds
Long sleep progress updated: +65535 seconds, total: 65535 seconds
Sleep for 65535 seconds
```

#### 第一次唤醒
```
Long sleep in progress: 65535/172800 seconds, remaining: 107265 seconds (29.8 hours)
Long sleep tracking: continuing sleep cycle
Sleep for 65535 seconds
```

#### 第二次唤醒
```
Long sleep in progress: 131070/172800 seconds, remaining: 41730 seconds (11.6 hours)
Long sleep tracking: continuing sleep cycle
Sleep for 41730 seconds
```

#### 第三次唤醒
```
Long sleep completed: 172800/172800 seconds
Battery: 4.098 V
[正常运行流程...]
```

## 适用场景

### 1. 常规长时间休眠
- **D1-D365**: 1天到1年的休眠
- **H19-H8760**: 19小时到1年的休眠

### 2. 工作时间段智能休眠
- 当计算出的智能休眠时间 > 18.2小时时自动启用

### 3. 示例用例
```
ZL+D7     // 7天休眠，分7次执行，每次18.2小时
ZL+H100   // 100小时休眠，分6次执行
ZL+F08E09 // 如果当前23:00，到明天08:00需要9小时，正常执行
ZL+F08E09 // 如果当前10:00，到明天08:00需要22小时，启用跟踪
```

## 优势特点

### 1. 透明性
- 从服务器角度看，设备正常执行了长时间休眠指令
- 用户无需关心内部实现细节

### 2. 可靠性
- 每次唤醒都进行自检，确保系统正常
- 进度保存在RAM中，重启后会重置（这是合理的）

### 3. 高效性
- 只在必要时启用跟踪机制
- 短时间休眠（≤18.2小时）不受影响

### 4. 调试友好
- 详细的进度输出
- 清晰的状态转换信息

## 注意事项

### 1. 内存使用
- 新增3个全局变量，占用约9字节RAM
- 对系统资源影响极小

### 2. 重启行为
- 设备重启会清除跟踪状态
- 这是合理的，因为重启通常意味着异常或人为干预

### 3. 电源管理
- 每次唤醒都会进行电压检测
- 低电压保护优先级高于长时间休眠

### 4. 功能优先级
长时间休眠检查的优先级：
1. **长时间休眠跟踪** (最高)
2. 电压保护检查
3. 工作时间段检查
4. N指令检查
5. 正常功能流程

## 测试建议

### 1. 短期测试
```
ZL+H19  // 19小时，应该分2次执行
```

### 2. 中期测试
```
ZL+D1   // 1天，应该分2次执行
```

### 3. 长期测试
```
ZL+D7   // 7天，应该分多次执行
```

### 4. 边界测试
```
ZL+H18  // 18小时，应该一次执行（不启用跟踪）
ZL+S65535 // 65535秒，应该一次执行（不启用跟踪）
```

## 总结

长时间休眠跟踪机制完美解决了RTC硬件限制问题：

- ✅ **真正执行长时间休眠指令**
- ✅ **服务器端看到正确的设备行为**
- ✅ **定期自检确保系统健康**
- ✅ **对现有功能零影响**
- ✅ **调试信息完整清晰**

现在设备可以真正执行D2、D7甚至D365这样的超长休眠指令了！
