

################################################################
## 网络下发指令使用LPUART1串口中断接收模式，保证设备在工作周期内任何时间发送指令都可以正确接收
## 网络指令的响应你评估一下放在哪个线程里处理比较合适
## 网络下发的自定义指令要保存到单片机内部FLASH空间中
## GSM模块是唯一的通信模块，AT指令发送和网络指令接收都使用LPUART1串口
## 具体需要接收的数据类型如下：

ZL+S10+F16E16+N20

ZL+ 自定义指令标识
D 表示 天  设备休眠时间设置
H 表示 时  设备休眠时间设置
M 表示 分  设备休眠时间设置
S 表示 秒  设备休眠时间设置
N 表示 打包N条数据一起发送  （只接收 暂时不用）
F 表示 工作时间段设置标识，F表示开始时间 E 表示结束时间 示例：ZL+F16E22 表示工作时间段是16点——22点 内工作

## 设备休眠时间只占一个位置 并且是覆盖机制 比如：D1 或 H12 或M5 或S30 只保存当前接收的指令覆盖旧指令
## bsp_flash.c存储代码设置的每个位置8个字节大小，足够任何格式的指令进行存储，检查是否支持字符串存储
/* 注意：内部flash由于环形缓冲区信息占用了14-15索引，因此用户只能使用索引0-13 */
/* 索引14：存储环形缓冲区的记录数量信息（高16位：总记录数，低16位：已读记录数） */
/* 索引15：存储环形缓冲区的当前写入地址 */







