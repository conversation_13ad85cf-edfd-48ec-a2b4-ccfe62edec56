/**
  ******************************************************************************
  * @file    bsp_spi_flash.c
  * @brief   SPI Flash driver source file
  ******************************************************************************
  */

#include "SPI_FLASH/bsp_spi_flash.h"
#include "FLASH/bsp_flash.h"
#include "spi.h"
#include <string.h>
#include <stdio.h>

#define SPI_FLASH_CS_LOW()    HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_RESET)
#define SPI_FLASH_CS_HIGH()   HAL_GPIO_WritePin(CS_GPIO_Port, <PERSON>_Pi<PERSON>, GPIO_PIN_SET)

SPI_FLASH_RingBuffer_t RingBuffer;
static uint8_t IsRingBufferInitialized = 0;

static HAL_StatusTypeDef SPI_FLASH_WaitForWriteEnd(void);
static HAL_StatusTypeDef SPI_FLASH_WriteEnable(void);
static HAL_StatusTypeDef SPI_FLASH_WritePage(uint32_t WriteAddr, uint8_t *pData, uint16_t Size);
HAL_StatusTypeDef SPI_FLASH_SaveRingBufferInfo(void);
static HAL_StatusTypeDef SPI_FLASH_LoadRingBufferInfo(void);

// Wait for SPI Flash write completion
static HAL_StatusTypeDef SPI_FLASH_WaitForWriteEnd(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_READ_STATUS;
  uint8_t statusReg = 0;
  uint32_t timeout = 0xFFFFFF;

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  do
  {
    status = HAL_SPI_Receive(&hspi1, &statusReg, 1, 100);
    if (status != HAL_OK)
    {
      SPI_FLASH_CS_HIGH();
      return status;
    }

    timeout--;
  } while ((statusReg & SPI_FLASH_STATUS_BUSY) && (timeout > 0));

  SPI_FLASH_CS_HIGH();

  if (timeout == 0)
  {
    return HAL_TIMEOUT;
  }

  return HAL_OK;
}

// Enable SPI Flash write
static HAL_StatusTypeDef SPI_FLASH_WriteEnable(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_WRITE_ENABLE;

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);

  SPI_FLASH_CS_HIGH();

  return status;
}

// Write one page of data to SPI Flash
static HAL_StatusTypeDef SPI_FLASH_WritePage(uint32_t WriteAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  if (Size > SPI_FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_PAGE_PROGRAM;
  cmd[1] = (WriteAddr >> 16) & 0xFF;
  cmd[2] = (WriteAddr >> 8) & 0xFF;
  cmd[3] = WriteAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  status = HAL_SPI_Transmit(&hspi1, pData, Size, 1000);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Save ring buffer info to internal Flash
HAL_StatusTypeDef SPI_FLASH_SaveRingBufferInfo(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = Flash_WriteUint32(FLASH_RING_BUFFER_ADDR_INDEX, RingBuffer.CurrentAddr);
  if (status != HAL_OK)
  {
    return status;
  }

  uint32_t recordInfo = ((RingBuffer.TotalRecords & 0xFFFF) << 16) | (RingBuffer.ReadRecords & 0xFFFF);
  status = Flash_WriteUint32(FLASH_RING_BUFFER_COUNT_INDEX, recordInfo);
  if (status != HAL_OK)
  {
    return status;
  }

  return HAL_OK;
}

// Load ring buffer info from internal Flash
static HAL_StatusTypeDef SPI_FLASH_LoadRingBufferInfo(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = Flash_ReadUint32(FLASH_RING_BUFFER_ADDR_INDEX, &RingBuffer.CurrentAddr);
  if (status != HAL_OK)
  {
    return status;
  }

  uint32_t recordInfo;
  status = Flash_ReadUint32(FLASH_RING_BUFFER_COUNT_INDEX, &recordInfo);
  if (status != HAL_OK)
  {
    return status;
  }

  RingBuffer.TotalRecords = (recordInfo >> 16) & 0xFFFF;
  RingBuffer.ReadRecords = recordInfo & 0xFFFF;

  return HAL_OK;
}

// Initialize SPI Flash
HAL_StatusTypeDef SPI_FLASH_Init(void)
{
  SPI_FLASH_CS_HIGH();

  HAL_Delay(100);

  return SPI_FLASH_InitRingBuffer();
}

// Read SPI Flash ID
HAL_StatusTypeDef SPI_FLASH_ReadID(SPI_FLASH_ID_t *ID)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_READ_ID;
  uint8_t id[3];

  if (ID == NULL)
  {
    return HAL_ERROR;
  }

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  status = HAL_SPI_Receive(&hspi1, id, 3, 100);

  SPI_FLASH_CS_HIGH();

  if (status == HAL_OK)
  {
    ID->Manufacturer = id[0];
    ID->MemoryType = id[1];
    ID->Capacity = id[2];
  }

  return status;
}

// Erase SPI Flash sector
HAL_StatusTypeDef SPI_FLASH_EraseSector(uint32_t SectorAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_SECTOR_ERASE;
  cmd[1] = (SectorAddr >> 16) & 0xFF;
  cmd[2] = (SectorAddr >> 8) & 0xFF;
  cmd[3] = SectorAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase SPI Flash block (32KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock32K(uint32_t BlockAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_BLOCK_ERASE_32K;
  cmd[1] = (BlockAddr >> 16) & 0xFF;
  cmd[2] = (BlockAddr >> 8) & 0xFF;
  cmd[3] = BlockAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase SPI Flash block (64KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock64K(uint32_t BlockAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_BLOCK_ERASE_64K;
  cmd[1] = (BlockAddr >> 16) & 0xFF;
  cmd[2] = (BlockAddr >> 8) & 0xFF;
  cmd[3] = BlockAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase entire SPI Flash
HAL_StatusTypeDef SPI_FLASH_EraseChip(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_CHIP_ERASE;

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Read SPI Flash data
HAL_StatusTypeDef SPI_FLASH_ReadData(uint32_t ReadAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];
  uint32_t primask;

  if (pData == NULL || Size == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_READ_DATA;
  cmd[1] = (ReadAddr >> 16) & 0xFF;
  cmd[2] = (ReadAddr >> 8) & 0xFF;
  cmd[3] = ReadAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  status = HAL_SPI_Receive(&hspi1, pData, Size, 1000);

  SPI_FLASH_CS_HIGH();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Write SPI Flash data
HAL_StatusTypeDef SPI_FLASH_WriteData(uint32_t WriteAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint16_t pageRemain;
  uint16_t currentSize;
  uint32_t currentAddr;
  uint16_t currentOffset;
  uint32_t primask;

  if (pData == NULL || Size == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  currentAddr = WriteAddr;
  currentOffset = 0;

  while (currentOffset < Size)
  {
    pageRemain = SPI_FLASH_PAGE_SIZE - (currentAddr % SPI_FLASH_PAGE_SIZE);

    currentSize = (currentOffset + pageRemain <= Size) ? pageRemain : (Size - currentOffset);

    status = SPI_FLASH_WritePage(currentAddr, &pData[currentOffset], currentSize);
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }

    currentAddr += currentSize;
    currentOffset += currentSize;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Initialize ring buffer
HAL_StatusTypeDef SPI_FLASH_InitRingBuffer(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = SPI_FLASH_LoadRingBufferInfo();

  if (status != HAL_OK ||
      RingBuffer.CurrentAddr > SPI_FLASH_RING_BUFFER_END)
  {
    RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START;
    RingBuffer.TotalRecords = 0;
    RingBuffer.ReadRecords = 0;

    status = SPI_FLASH_SaveRingBufferInfo();
    if (status != HAL_OK)
    {
      return status;
    }

    status = SPI_FLASH_EraseBlock64K(SPI_FLASH_RING_BUFFER_START);
    if (status != HAL_OK)
    {
      return status;
    }
  }

  IsRingBufferInitialized = 1;

  return HAL_OK;
}

// Write record to ring buffer
// 修复后的写入函数（主要是记录管理逻辑）
HAL_StatusTypeDef SPI_FLASH_WriteRecord(uint8_t *pData, uint16_t Size)
{
    HAL_StatusTypeDef status = HAL_OK;
    uint8_t recordBuffer[SPI_FLASH_RECORD_SIZE];
    uint32_t primask;
    uint32_t recordIndex;
    uint32_t writeAddr;
    uint32_t sectorAddr;

    if (pData == NULL || Size == 0 || Size > SPI_FLASH_RECORD_SIZE) {
        return HAL_ERROR;
    }

    primask = __get_PRIMASK();
    __disable_irq();

    if (!IsRingBufferInitialized) {
        status = SPI_FLASH_InitRingBuffer();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    // 环形缓冲区写入地址计算
    recordIndex = RingBuffer.TotalRecords % SPI_FLASH_MAX_RECORDS;
    writeAddr = SPI_FLASH_RING_BUFFER_START + (recordIndex * SPI_FLASH_RECORD_SIZE);

    // 打印写入地址信息（用于调试）
    printf("SPI Flash Write: RecordIndex=%lu, WriteAddr=0x%08X, TotalRecords=%lu, ReadRecords=%lu\r\n",
           recordIndex, writeAddr, RingBuffer.TotalRecords, RingBuffer.ReadRecords);

    // 擦除sector（每4096字节一次）
    sectorAddr = (writeAddr / SPI_FLASH_SECTOR_SIZE) * SPI_FLASH_SECTOR_SIZE;
    if ((writeAddr % SPI_FLASH_SECTOR_SIZE) == 0) {
        status = SPI_FLASH_EraseSector(sectorAddr);
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    // 准备写入数据
    memset(recordBuffer, 0xFF, SPI_FLASH_RECORD_SIZE);
    memcpy(recordBuffer, pData, Size);

    // 打印写入的数据内容（用于调试）
//    printf("SPI Flash Data Write: %s\r\n", (char*)recordBuffer);

    status = SPI_FLASH_WriteData(writeAddr, recordBuffer, SPI_FLASH_RECORD_SIZE);
    if (status != HAL_OK) {
        if (!primask) { __enable_irq(); }
        return status;
    }

    // 更新指针和计数
    RingBuffer.CurrentAddr = writeAddr + SPI_FLASH_RECORD_SIZE;
    if (RingBuffer.CurrentAddr > SPI_FLASH_RING_BUFFER_END) {
        RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START;
    }
    RingBuffer.TotalRecords++;

    // 环形缓冲区管理：当超过最大记录数时，自动调整ReadRecords
    if (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) {
        uint32_t oldestValidRecord = RingBuffer.TotalRecords - SPI_FLASH_MAX_RECORDS;
        if (RingBuffer.ReadRecords < oldestValidRecord) {
            RingBuffer.ReadRecords = oldestValidRecord;
            printf("Ring buffer wrapped: ReadRecords adjusted to %lu\r\n", RingBuffer.ReadRecords);
        }
    }

    status = SPI_FLASH_SaveRingBufferInfo();

    if (!primask) { __enable_irq(); }
    return status;
}

// 读取记录（线性覆盖逻辑）
HAL_StatusTypeDef SPI_FLASH_ReadRecordEx(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size, uint8_t MarkAsRead)
{
    HAL_StatusTypeDef status = HAL_OK;
    uint32_t primask;
    uint32_t totalRecords;
    uint32_t physicalIndex;
    uint32_t readAddr;

    if (pData == NULL || Size == NULL || *Size == 0) {
        return HAL_ERROR;
    }

    primask = __get_PRIMASK();
    __disable_irq();

    if (!IsRingBufferInitialized) {
        status = SPI_FLASH_InitRingBuffer();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    totalRecords = RingBuffer.TotalRecords;
    if (totalRecords == 0) {
        if (!primask) { __enable_irq(); }
        return HAL_ERROR;
    }

    // 未读记录数
    uint32_t unreadCount = totalRecords - RingBuffer.ReadRecords;
    if (RecordIndex >= unreadCount) {
        if (!primask) { __enable_irq(); }
        return HAL_ERROR;
    }

    // 环形缓冲区地址计算：计算实际要读取的记录编号
    uint32_t actualRecordNumber = RingBuffer.ReadRecords + RecordIndex;

    // 转换为物理地址索引（环形缓冲区的核心逻辑）
    physicalIndex = actualRecordNumber % SPI_FLASH_MAX_RECORDS;
    readAddr = SPI_FLASH_RING_BUFFER_START + (physicalIndex * SPI_FLASH_RECORD_SIZE);

    // 打印读取地址信息（用于调试）
    if (RecordIndex < 10 || RecordIndex > 500) {  // 只打印前10条和500条后的调试信息
        printf("SPI Flash Read: RecordIndex=%lu, ActualRecordNumber=%lu, PhysicalIndex=%lu, ReadAddr=0x%08X, TotalRecords=%lu, ReadRecords=%lu\r\n",
               RecordIndex, actualRecordNumber, physicalIndex, readAddr, RingBuffer.TotalRecords, RingBuffer.ReadRecords);
    }

    status = SPI_FLASH_ReadData(readAddr, pData, SPI_FLASH_RECORD_SIZE);
    if (status != HAL_OK) {
        if (!primask) { __enable_irq(); }
        return status;
    }

    // 打印读取到的原始数据（用于调试）
    printf("SPI Flash Data Read: %s\r\n", (char*)pData);

    *Size = SPI_FLASH_RECORD_SIZE;

    // 标记为已读
    if (MarkAsRead && RingBuffer.ReadRecords < RingBuffer.TotalRecords && RecordIndex == 0) {
        RingBuffer.ReadRecords++;
        status = SPI_FLASH_SaveRingBufferInfo();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    if (!primask) { __enable_irq(); }
    return HAL_OK;
}

// Read record from ring buffer（兼容旧接口，直接调用Ex版本，MarkAsRead=1）
HAL_StatusTypeDef SPI_FLASH_ReadRecord(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size)
{
    return SPI_FLASH_ReadRecordEx(RecordIndex, pData, Size, 1);
}

// 获取未读记录数（环形缓冲区智能计数）
uint32_t SPI_FLASH_GetRecordCount(void)
{
    if (!IsRingBufferInitialized) {
        if (SPI_FLASH_InitRingBuffer() != HAL_OK) {
            return 0;
        }
    }

    uint32_t totalRecords = RingBuffer.TotalRecords;

    if (totalRecords <= SPI_FLASH_MAX_RECORDS) {
        // 未满情况：简单计算
        return totalRecords - RingBuffer.ReadRecords;
    } else {
        // 已满情况：环形缓冲区逻辑
        uint32_t oldestValidRecord = totalRecords - SPI_FLASH_MAX_RECORDS;

        // 自动调整ReadRecords，跳过被覆盖的数据
        if (RingBuffer.ReadRecords < oldestValidRecord) {
            RingBuffer.ReadRecords = oldestValidRecord;
            SPI_FLASH_SaveRingBufferInfo();
            printf("Auto-adjusted ReadRecords to %lu (oldest valid: %lu)\r\n",
                   RingBuffer.ReadRecords, oldestValidRecord);
        }

        return totalRecords - RingBuffer.ReadRecords;
    }
}

// 获取总记录数（只返回最大容量内的最新记录数）
uint32_t SPI_FLASH_GetTotalRecordCount(void)
{
    if (!IsRingBufferInitialized) {
        if (SPI_FLASH_InitRingBuffer() != HAL_OK) {
            return 0;
        }
    }

    uint32_t totalRecords = RingBuffer.TotalRecords;
    if (totalRecords > SPI_FLASH_MAX_RECORDS) {
        totalRecords = SPI_FLASH_MAX_RECORDS;
    }
    return totalRecords;
}

// Clear ring buffer
HAL_StatusTypeDef SPI_FLASH_ClearRingBuffer(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = SPI_FLASH_EraseBlock64K(SPI_FLASH_RING_BUFFER_START);
  if (status != HAL_OK)
  {
    return status;
  }

  RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START;
  RingBuffer.TotalRecords = 0;
  RingBuffer.ReadRecords = 0;

  status = SPI_FLASH_SaveRingBufferInfo();

  return status;
}

// Mark all records as read
HAL_StatusTypeDef SPI_FLASH_MarkAllAsRead(void)
{
  if (!IsRingBufferInitialized)
  {
    if (SPI_FLASH_InitRingBuffer() != HAL_OK)
    {
      return HAL_ERROR;
    }
  }

  RingBuffer.ReadRecords = RingBuffer.TotalRecords;

  return SPI_FLASH_SaveRingBufferInfo();
}

// Mark multiple records as read (for batch processing)
HAL_StatusTypeDef SPI_FLASH_MarkMultipleAsRead(uint32_t count)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t primask;
  uint32_t unreadRecords;

  if (count == 0) {
    return HAL_OK;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!IsRingBufferInitialized) {
    status = SPI_FLASH_InitRingBuffer();
    if (status != HAL_OK) {
      if (!primask) { __enable_irq(); }
      return status;
    }
  }

  unreadRecords = SPI_FLASH_GetRecordCount();

  // 确保不会标记超过未读记录数的记录
  if (count > unreadRecords) {
    count = unreadRecords;
  }

  // 批量标记为已读
  RingBuffer.ReadRecords += count;

  // 确保ReadRecords不会超过TotalRecords
  if (RingBuffer.ReadRecords > RingBuffer.TotalRecords) {
    RingBuffer.ReadRecords = RingBuffer.TotalRecords;
  }

  status = SPI_FLASH_SaveRingBufferInfo();

  if (!primask) { __enable_irq(); }

  printf("Marked %lu records as read, remaining unread: %lu\r\n",
         count, SPI_FLASH_GetRecordCount());

  return status;
}

// Mark specific record as read
HAL_StatusTypeDef SPI_FLASH_MarkRecordAsRead(uint32_t RecordIndex)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t recordCount;
  uint32_t unreadRecords;
  uint32_t primask;

  primask = __get_PRIMASK();
  __disable_irq();

  if (!IsRingBufferInitialized)
  {
    status = SPI_FLASH_InitRingBuffer();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  recordCount = SPI_FLASH_GetTotalRecordCount();
  unreadRecords = SPI_FLASH_GetRecordCount();

  if (RecordIndex >= recordCount)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  if (RecordIndex == 0 && unreadRecords > 0)
  {
    RingBuffer.ReadRecords++;
    if (RingBuffer.ReadRecords > RingBuffer.TotalRecords)
    {
      RingBuffer.ReadRecords = RingBuffer.TotalRecords;
    }
    status = SPI_FLASH_SaveRingBufferInfo();
  }
  else if (RecordIndex > 0 && RecordIndex < unreadRecords)
  {
    status = HAL_ERROR;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// 批量读取环形缓冲区数据并连续输出，无中间调试信息
HAL_StatusTypeDef SPI_FLASH_ReadMultipleRecords(uint32_t MaxRecords)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t read_buffer[SPI_FLASH_RECORD_SIZE];
  uint16_t data_size;
  uint32_t unread_count;
  uint32_t read_count;
  uint32_t success_count = 0;
  uint32_t error_count = 0;

  // 获取未读记录数量
  unread_count = SPI_FLASH_GetRecordCount();
  if (unread_count == 0) {
    printf("No unread records available\r\n");
    return HAL_OK;
  }

  // 计算实际读取数量
  read_count = (unread_count > MaxRecords) ? MaxRecords : unread_count;

  // 只在开始时输出一次
  printf("Start reading %lu records (unread: %lu)\r\n", read_count, unread_count);

  // 简化的读取循环：测试读取指定数量的记录（不标记为已读）
  for (uint32_t i = 0; i < read_count; i++) {
    // 测试读取模式，不需要检查未读记录数

    // 读取记录（测试读取，不标记为已读）
    data_size = SPI_FLASH_RECORD_SIZE;
    status = SPI_FLASH_ReadRecordEx(i, read_buffer, &data_size, 0);  // 读取索引i，不标记为已读

    if (status == HAL_OK) {
      // 检查数据是否有效
      if (data_size > 0 && read_buffer[0] != 0xFF && read_buffer[0] != 0x00) {
        printf("Record %lu: %s\r\n", i + 1, (char*)read_buffer);  // 显示测试读取的数据
        success_count++;
      } else {
        // 数据无效（测试读取模式，未标记为已读）
        printf("Record %lu: Invalid data\r\n", i + 1);
        error_count++;
      }
    } else {
      // 读取失败
      error_count++;
      printf("Read error at record %lu\r\n", i + 1);
    }

    // 短暂延迟，避免过快操作
    HAL_Delay(5);
  }

  // 输出结果统计
  printf("Read completed: %lu valid, %lu errors\r\n", success_count, error_count);
  return HAL_OK;
}

// Print ring buffer status for debugging
void SPI_FLASH_PrintRingBufferStatus(void)
{
    printf("=== Ring Buffer Status ===\r\n");
    printf("Current Address: 0x%08X\r\n", RingBuffer.CurrentAddr);
    printf("Total Records: %lu\r\n", RingBuffer.TotalRecords);
    printf("Read Records: %lu\r\n", RingBuffer.ReadRecords);
    printf("Unread Records: %lu\r\n", SPI_FLASH_GetRecordCount());
    printf("Max Capacity: %lu records\r\n", SPI_FLASH_MAX_RECORDS);
    printf("Buffer Size: %lu bytes\r\n", SPI_FLASH_RING_BUFFER_SIZE);
    printf("Record Size: %d bytes\r\n", SPI_FLASH_RECORD_SIZE);

    if (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) {
        printf("STATUS: Ring buffer has wrapped around!\r\n");
        uint32_t oldestValid = RingBuffer.TotalRecords - SPI_FLASH_MAX_RECORDS;
        printf("Oldest Valid Record: %lu\r\n", oldestValid);
        printf("Current Write Index: %lu\r\n", RingBuffer.TotalRecords % SPI_FLASH_MAX_RECORDS);
    } else {
        printf("STATUS: Ring buffer not wrapped\r\n");
    }
    printf("========================\r\n");
}
