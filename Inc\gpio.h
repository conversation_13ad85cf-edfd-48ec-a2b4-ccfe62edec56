/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.h
  * @brief   This file contains all the function prototypes for
  *          the gpio.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __GPIO_H__
#define __GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */
#define LED1_OFF        HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_RESET)    // LED1关闭，低电平有效
#define LED1_ON         HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_SET)      // LED1打开，高电平有效
#define LED1_TOGGLE     HAL_GPIO_TogglePin(GPIOC, LED1_Pin)                   // LED1状态翻转

// 电源管理函数声明 - 硬件缺陷已修复，使用正常输出模式控制
void PowerPins_InitForWakeup(void);        // 电源引脚初始化为输出模式（唤醒后调用）

// 更新宏定义，使用正常输出模式控制 - 硬件缺陷已修复
#define RF_PWR_OFF      HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_RESET)      // 通信模块电源关闭（高电平）
#define RF_PWR_ON       HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_SET)    // 通信模块电源打开（低电平）

#define GPS_PWR_OFF     HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_RESET)     // GPS电源关闭（高电平）
#define GPS_PWR_ON      HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_SET)   // GPS电源打开（低电平）

#define CAM_PW_OFF      HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_SET)      // 摄像头电源关闭（高电平）
#define CAM_PW_ON       HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_RESET)    // 摄像头电源打开（低电平）

#define V_OUT_ON        HAL_GPIO_WritePin(GPIOA, V_OUT_Pin, GPIO_PIN_SET)        // 外设电源打开，高电平有效
#define V_OUT_OFF       HAL_GPIO_WritePin(GPIOA, V_OUT_Pin, GPIO_PIN_RESET)      // 外设电源关闭，低电平有效

#define VCHK_ON         HAL_GPIO_WritePin(GPIOA, VCHK_Pin, GPIO_PIN_SET)         // AD采样开关打开，高电平有效
#define VCHK_OFF        HAL_GPIO_WritePin(GPIOA, VCHK_Pin, GPIO_PIN_RESET)       // AD采样开关关闭，低电平有效
/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_GPIO_Init(void);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ GPIO_H__ */

