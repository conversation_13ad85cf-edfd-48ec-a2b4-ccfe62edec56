# AB指令测试说明

## 概述

为了方便测试AB指令（速度阈值和快发模式），我们添加了虚拟速度测试功能，并将速度单位改为使用GPS原始值（节 knots）。

## 修改内容

### 1. 添加测试宏定义
**文件**: `Src/freertos.c`

```c
// AB指令测试宏（0=使用真实GPS速度，1=使用虚拟速度）
#define AB_COMMAND_TEST_ENABLE 1

// 虚拟速度值（用于AB指令测试，单位：节 knots）
#define VIRTUAL_SPEED_VALUE 15.0f  // 可以修改这个值来测试不同速度
```

### 2. 速度单位变更
- **之前**: 使用km/h（GPS速度 × 1.852转换）
- **现在**: 使用节（knots，GPS原始值）
- **原因**: 简化逻辑，避免单位转换的复杂性

### 3. 虚拟速度功能
当`AB_COMMAND_TEST_ENABLE = 1`时：
- 使用`VIRTUAL_SPEED_VALUE`替代真实GPS速度
- 显示调试信息对比虚拟速度和真实速度
- 便于测试不同速度下的AB指令行为

## 测试方法

### 步骤1: 启用测试模式
确保`AB_COMMAND_TEST_ENABLE = 1`

### 步骤2: 设置虚拟速度
修改`VIRTUAL_SPEED_VALUE`为想要测试的速度值（节）

### 步骤3: 发送AB指令
例如：`ZL+A10+B120`
- A10: 速度阈值10节
- B120: 快发模式休眠120秒

### 步骤4: 观察测试结果

#### 当虚拟速度 > 阈值时
```
AB Test Mode: Using virtual speed 15.0 knots (Real GPS: 2.3 knots)
Speed check: current=15.0 knots, threshold=10 knots
Speed threshold exceeded - using fast send sleep time: 120 seconds
Using pre-set sleep time: 120 seconds
```

#### 当虚拟速度 <= 阈值时
```
AB Test Mode: Using virtual speed 8.0 knots (Real GPS: 2.3 knots)
Speed check: current=8.0 knots, threshold=10 knots
Speed below threshold - using normal sleep time
Using sleep setting from FLASH: 30 seconds
```

## 测试用例

### 测试用例1: 基本功能测试
1. 设置`VIRTUAL_SPEED_VALUE = 15.0f`
2. 发送`ZL+A10+B120`
3. 预期结果：使用120秒休眠时间

### 测试用例2: 边界值测试
1. 设置`VIRTUAL_SPEED_VALUE = 10.0f`
2. 发送`ZL+A10+B120`
3. 预期结果：使用正常休眠时间（不触发快发模式）

### 测试用例3: 禁用测试
1. 发送`ZL+A0`（禁用A指令）
2. 预期结果：不进行速度检查，使用正常休眠时间

### 测试用例4: 只有A指令
1. 设置`VIRTUAL_SPEED_VALUE = 15.0f`
2. 发送`ZL+A10`（没有B指令）
3. 预期结果：使用默认快发时间600秒

## 速度单位对照表

| 节 (knots) | km/h | 说明 |
|------------|------|------|
| 1 | 1.852 | 1节 = 1.852 km/h |
| 5 | 9.26 | 慢速行走 |
| 10 | 18.52 | 快速行走/慢跑 |
| 20 | 37.04 | 自行车速度 |
| 50 | 92.6 | 汽车市区速度 |
| 100 | 185.2 | 汽车高速速度 |

## 常用测试配置

### 低速测试（步行）
```c
#define VIRTUAL_SPEED_VALUE 3.0f  // 约5.5 km/h
```
发送：`ZL+A5+B60`

### 中速测试（自行车）
```c
#define VIRTUAL_SPEED_VALUE 15.0f  // 约27.8 km/h
```
发送：`ZL+A10+B120`

### 高速测试（汽车）
```c
#define VIRTUAL_SPEED_VALUE 50.0f  // 约92.6 km/h
```
发送：`ZL+A30+B300`

## 调试输出说明

### 测试模式开启时
```
AB Test Mode: Using virtual speed 15.0 knots (Real GPS: 2.3 knots)
Speed threshold setting updated: 10 knots, enabled=1
Fast send sleep time updated: 120 seconds
Speed check: current=15.0 knots, threshold=10 knots
Speed threshold exceeded - using fast send sleep time: 120 seconds
```

### 测试模式关闭时
```
Speed threshold setting updated: 10 knots, enabled=1
Fast send sleep time updated: 120 seconds
Speed check: current=2.3 knots, threshold=10 knots
Speed below threshold - using normal sleep time
```

## 注意事项

1. **编译前设置**: 修改宏定义后需要重新编译
2. **测试完成后**: 记得将`AB_COMMAND_TEST_ENABLE`改为0以使用真实GPS速度
3. **速度范围**: A指令限制为1-200节（约1.8-370 km/h）
4. **默认值**: B指令默认值为600秒
5. **兼容性**: 与N指令和其他功能完全兼容

## 生产环境配置

测试完成后，生产环境应该设置：
```c
#define AB_COMMAND_TEST_ENABLE 0  // 禁用测试模式，使用真实GPS速度
```

这样设备将使用真实的GPS速度进行AB指令判断。
