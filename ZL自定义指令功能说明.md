# ZL自定义指令功能说明

## 概述
本文档描述了新增的ZL自定义指令功能，包括N指令（数据存储控制）、A指令（速度阈值）和B指令（快发模式休眠时间）。

## 新增指令

### 1. N指令 - 存储N条数据后再启动GSM模块
- **格式**: `ZL+N<数值>`
- **功能**: 设置存储N条数据后再启动GSM模块进行发送
- **示例**: `ZL+N10` 表示存储10条数据后发送
- **限制**: 1-1000条
- **逻辑**: 
  - 当N > 5时，启用此功能
  - 设备获取GPS数据后合成B数据并存储到SPI Flash
  - 检测历史数据条数，如果 >= N，则启动GSM模块发送
  - 如果 < N，则跳过GSM模块，直接进入休眠

### 2. A指令 - 速度阈值设置
- **格式**: `ZL+A<数值>`
- **功能**: 设置速度阈值（km/h），超过阈值触发快发模式
- **示例**: `ZL+A10` 表示速度阈值为10 km/h
- **限制**: 1-300 km/h
- **逻辑**:
  - 当A > 0时，启用速度阈值功能
  - 数据发送成功后检查当前GPS速度
  - 如果速度 > A，使用B指令设置的休眠时间
  - 如果速度 <= A，使用正常休眠时间

### 3. B指令 - 快发模式休眠时间
- **格式**: `ZL+B<数值>`
- **功能**: 设置快发模式下的休眠时间（秒）
- **示例**: `ZL+B300` 表示快发模式休眠300秒
- **限制**: 1-86400秒（1天）
- **默认值**: 600秒
- **逻辑**:
  - 当速度超过A指令阈值时使用此休眠时间
  - 如果只有A指令没有B指令，默认使用600秒

## 指令组合示例

### 示例1: 基本速度阈值功能
```
ZL+A10+B300
```
- 速度阈值: 10 km/h
- 快发模式休眠时间: 300秒
- 当速度 > 10 km/h时，休眠300秒
- 当速度 <= 10 km/h时，使用正常休眠时间

### 示例2: 数据存储控制
```
ZL+N20
```
- 存储20条数据后再启动GSM模块
- 每次唤醒都获取GPS数据并存储
- 只有当历史数据 >= 20条时才启动GSM发送

### 示例3: 完整功能组合
```
ZL+S30+A10+B300+N20
```
- 正常休眠时间: 30秒
- 速度阈值: 10 km/h
- 快发模式休眠时间: 300秒
- 数据存储阈值: 20条

## 数据处理流程

### 正常模式（无N指令或N <= 5）
1. 获取GPS数据
2. 合成S数据和B数据
3. 启动GSM模块
4. 发送S数据
5. 如果发送成功：
   - 检查速度阈值（如果启用A指令）
   - 设置相应的休眠时间
   - 丢弃B数据
6. 如果发送失败：
   - 保存B数据到SPI Flash
7. 进入休眠

### N指令模式（N > 5）
1. 获取GPS数据
2. 合成B数据
3. 检查历史数据条数
4. 如果历史数据 < N：
   - 直接保存B数据到SPI Flash
   - 跳过GSM模块
   - 进入休眠
5. 如果历史数据 >= N：
   - 启动GSM模块
   - 发送实时数据和历史数据
   - 检查速度阈值（如果启用）
   - 进入休眠

## 兼容性说明

### 与现有机制的兼容性
- **完全兼容**现有的S数据和B数据机制
- **完全兼容**现有的历史数据存储和发送机制
- **完全兼容**现有的工作时间段功能
- **完全兼容**现有的休眠时间设置（D/H/M/S指令）

### GPS速度单位处理
- GPS原始速度单位：节（knots）
- 系统内部转换：`speed_kmh = gps_speed * 1.852`
- A指令使用km/h作为单位，与用户习惯一致

## 存储机制

### FLASH存储索引
- FLASH_INDEX_PACK_COUNT (3): N指令值
- FLASH_INDEX_SPEED_THRESHOLD (4): A指令值
- FLASH_INDEX_FAST_SEND_SLEEP (5): B指令值

### 全局变量
- `pack_count_threshold`: N指令阈值
- `pack_count_enabled`: N指令是否启用
- `speed_threshold_kmh`: A指令速度阈值
- `speed_threshold_enabled`: A指令是否启用
- `fast_send_sleep_time`: B指令快发休眠时间

## 调试信息

系统会输出以下调试信息帮助监控功能状态：

### N指令相关
```
Pack count setting updated: threshold=20, enabled=1
Skip GSM this cycle - N command active
GSM will be activated - threshold reached (20 records)
```

### A/B指令相关
```
Speed threshold setting updated: 10 km/h, enabled=1
Fast send sleep time updated: 300 seconds
Speed check: current=15.2 km/h, threshold=10 km/h
Speed threshold exceeded - using fast send sleep time: 300 seconds
```

## 注意事项

1. **N指令只有在N > 5时才启用**，这是为了避免过于频繁的数据存储
2. **A指令只有在A > 0时才启用**，0值表示禁用速度阈值功能
3. **B指令的默认值是600秒**，即使没有设置B指令，A指令也能正常工作
4. **所有指令都会立即生效**，无需重启设备
5. **指令设置会保存到FLASH**，设备重启后自动加载
